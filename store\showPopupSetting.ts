import { create } from "zustand";

interface TypeShowPopupSetting {
    showPopup: string;
    setShowPopup: (item: string) => void;
    isAnalyse: boolean;
    setIsAnalyse: (item: boolean) => void;
}

export const showPopupSetting = create<TypeShowPopupSetting>((set) => ({
    showPopup: "ai",
    isAnalyse: false,
    setShowPopup: (item: string) => set({ showPopup: item }),
    setIsAnalyse: (item: boolean) => set({ isAnalyse: item }),
}));
